import AWS from 'aws-sdk';
import nodemailer from 'nodemailer';
import moment from 'moment-timezone';
import logger from '../utils/logger.js';
import { getEmailTemplate } from '../templates/email.templates.js';
import { generateEmailHtml } from '../templates/email/layout.js';
import { emitDeliveryMetric } from '../utils/metrics.js';

export default class AWSSESService {
  constructor() {
    AWS.config.update({ region: process.env.AWS_REGION });

    this.transporter = nodemailer.createTransport({
      SES: new AWS.SES({ apiVersion: '2010-12-01' }),
    });
  }

  async sendEmail(payload) {
    const { recipients, content, metadata } = payload;
    if (!recipients?.length) {
      throw new Error('No recipients provided for email');
    }

    let template;
    try {
      template = getEmailTemplate(metadata.eventName);
    } catch (error) {
      logger.error(`Template error for event: ${metadata.eventName}`, {
        metadata,
        error: error.message
      });
    }

    const dateString = moment().tz('Asia/Kolkata').format('YYYY-MM-DD');
    const threadId = `${metadata.alertInventoryId}.${dateString}@smartjoules.in`;

    let successCount = 0;
    let failureCount = 0;

    const emailPromises = recipients.map(async (recipient) => {
      const emailMetadata = { ...metadata, content };

      emailMetadata.description = content.body;

      const textContent = template.getBody(emailMetadata, content.title, recipient);

      const htmlContent = generateEmailHtml({
        metadata: emailMetadata,
        textContent
      });

      const threadSubject = `Alert #${metadata.alertInventoryId}: ${content.title}`;

      const mailOptions = {
        from: '<EMAIL>',
        to: recipient.email,
        subject: threadSubject,
        text: textContent || 'Please view this email in an HTML compatible email client.',
        html: htmlContent,
        messageId: threadId,
        references: threadId,
        headers: {
          'X-SJ-Alert-ID': metadata.alertInventoryId,
          'X-SJ-Alert-Severity': metadata.severity,
        }
      };

      const logContext = {
        recipient: recipient.email,
        transactionId: payload.transactionId,
        siteId: metadata.siteId,
        observer_execution_ref_id: metadata.observer_execution_ref_id,
        alertInventoryId: metadata.alertInventoryId,
        incidentId: metadata.incidentId,
        messageId: threadId,
      };

      try {
        await this.transporter.sendMail(mailOptions);
        successCount++;
        logger.info('🚀 Successfully sent email notification', logContext);
      } catch (error) {
        failureCount++;
        logger.error('Failed to send email notification', {
          ...logContext,
          error: error.message,
        });
      }
    });

    await Promise.all(emailPromises);

    await emitDeliveryMetric({
      metric_type: 'NOTIFICATION_DELIVERY',
      channel: 'EMAIL',
      delivery_status: failureCount === 0 ? 'SENT' : failureCount === recipients.length ? 'FAILED' : 'PARTIAL',
      device_type: metadata.deviceType,
      recipient_count: recipients.length,
      error_count: failureCount,
      transaction_id: payload.transactionId
    });
  }
}
