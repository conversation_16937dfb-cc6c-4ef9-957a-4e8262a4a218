import AWS from 'aws-sdk';
import moment from 'moment-timezone';
import * as Sentry from '@sentry/node';
import logger from '../utils/logger.js';
import postgresConnection from '../connection/postgres.js';

class BouncedEmailService {
  constructor() {
    this.documentClient = new AWS.DynamoDB.DocumentClient({
      region: process.env.AWS_REGION || 'us-west-2',
    });
  }

  /**
   * Check if email bounced 3+ times in last 7 days
   * @param {string} emailId - The email address to check
   * @returns {boolean} - True if email should be filtered
   */
  async isBouncedEmail(emailId) {
    try {
      const endTime = moment.tz('UTC');
      const startTime = moment.tz('UTC').subtract(7, 'days');

      const params = {
        TableName: 'bouncedemails',
        KeyConditionExpression: 'emailId = :emailId AND #timestamp BETWEEN :startTime AND :endTime',
        ExpressionAttributeNames: {
          '#timestamp': 'timestamp'
        },
        ExpressionAttributeValues: {
          ':emailId': emailId,
          ':startTime': startTime.toISOString(),
          ':endTime': endTime.toISOString()
        }
      };

      const result = await this.documentClient.query(params).promise();
      const bounceCount = result.Items.length;

      if (bounceCount >= 3) {
        try {
          const result = await postgresConnection.unsubscribeUserByEmail(emailId);
          if (result.unsubscribed) {
            logger.info(`Successfully unsubscribed bounced email: ${emailId} from ${result.alertCount} alerts`);
          } else {
            logger.info(`Could not unsubscribe ${emailId}: ${result.reason}`);
          }
        } catch (unsubError) {
          logger.error('Failed to unsubscribe bounced email', { emailId, error: unsubError.message });
        }

        return true;
      }

      return false;
    } catch (error) {
      logger.error('Error checking bounced email', { emailId, error: error.message });
      return false;
    }
  }



  /**
   * Filter recipients to remove bounced emails
   * @param {Array} recipients - Array of recipient objects with email property
   * @returns {Array} - Filtered array of valid recipients
   */
  async filterValidRecipients(recipients) {
    if (!Array.isArray(recipients)) {
      return recipients;
    }

    const validRecipients = [];
    const bouncedEmails = [];

    for (const recipient of recipients) {
      if (!recipient?.email) {
        continue;
      }

      const isBounced = await this.isBouncedEmail(recipient.email);
      if (!isBounced) {
        validRecipients.push(recipient);
      } else {
        bouncedEmails.push(recipient.email);

        // Log the bounced email error
        const errorMessage = `Bounced email: ${recipient.email}`;
        const errorContext = {
          email: recipient.email,
          reason: 'Email bounced 3+ times in last 7 days',
          action: 'filtered_from_recipients'
        };

        // Send to logger (which goes to Sentry via winston transport)
        logger.error(errorMessage, errorContext);

        // Also send directly to Sentry to ensure it's treated as production error
        Sentry.captureException(new Error(errorMessage), {
          tags: {
            component: 'bounced-email-service',
            action: 'filter_bounced_email'
          },
          extra: errorContext,
          level: 'error'
        });
      }
    }

    return validRecipients;
  }


}

export default new BouncedEmailService();
