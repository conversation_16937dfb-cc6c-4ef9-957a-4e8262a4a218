import AWSSESService from './AWSSES.service.js';
import bouncedEmailService from './bounced-email.service.js';

export default class NotificationFacade {
  constructor() {
    this.AWSSESServiceInstance = new AWSSESService();
  }

  async sendNotification(payload) {
    // Filter out bounced emails
    const filteredRecipients = await bouncedEmailService.filterValidRecipients(payload.recipients || []);

    if (filteredRecipients.length === 0) return;

    const filteredPayload = {
      ...payload,
      recipients: filteredRecipients
    };

    return await this.AWSSESServiceInstance.sendEmail(filteredPayload);
  }
}
