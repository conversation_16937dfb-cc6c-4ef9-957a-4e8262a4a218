import pkg from 'kafkajs';
import SnappyCodec from 'kafkajs-snappy';
import { promisify } from 'node:util';
import * as snappy from 'snappy';
import { v4 as uuidv4 } from 'uuid';
import kafkaConfig from '../config/kafka.js';
import logger from '../utils/logger.js';

const { Kafka, CompressionTypes, CompressionCodecs } = pkg;
CompressionCodecs[CompressionTypes.Snappy] = SnappyCodec;
const uncompressAsync = promisify(snappy.uncompress);

async function handleSnappyDecompression(message) {
  try {
    const result = await uncompressAsync(message, { asBuffer: true }, null);
    return result.toString();
  } catch (error) {
    error.message = `Failed to decompress message: ${error.message}`;
    throw error;
  }
}

class KafkaConnection {
  constructor() {
    if (KafkaConnection.instance) {
      return KafkaConnection.instance;
    }
    KafkaConnection.instance = this;
    this.client = null;
    this.consumer = null;
    this.producer = null;
    this.isConnected = false;
  }

  async initialize() {
    const { security, brokers, connectionTimeout } = kafkaConfig;

    try {
      if (security.protocol === 'SASL_SSL') {
        this.client = new Kafka({
          clientId: `email-notification-service-${uuidv4()}`,
          brokers,
          ssl: true,
          connectionTimeout,
          sasl: {
            mechanism: 'scram-sha-512',
            username: security.username,
            password: security.password,
          },
        });
      } else if (security.protocol === 'PLAIN') {
        this.client = new Kafka({
          clientId: `email-notification-service-${uuidv4()}`,
          brokers,
          connectionTimeout,
        });
      } else {
        throw new Error('Only PLAIN and SASL security protocol supported');
      }

      this.consumer = this.client.consumer({ groupId: process.env.KAFKA_CONSUMER_GROUP_ID || 'email-notification-group-id' });
      this.producer = this.client.producer();
    } catch (error) {
      error.message = `Failed to initialize Kafka client: ${error.message}`;
      throw error;
    }
  }

  async connect() {
    if (this.isConnected) {
      return;
    }

    try {
      if (!this.consumer || !this.producer) {
        await this.initialize();
      }
      await Promise.all([
        this.consumer.connect(),
        this.producer.connect()
      ]);
      this.isConnected = true;
      logger.info('Kafka client connected successfully');
    } catch (error) {
      this.isConnected = false;
      error.message = `Failed to connect Kafka client: ${error.message}`;
      throw error;
    }
  }

  async disconnect() {
    if (!this.isConnected) {
      return;
    }

    try {
      await Promise.all([
        this.consumer.disconnect(),
        this.producer.disconnect()
      ]);
      this.isConnected = false;
      logger.info('Kafka client disconnected successfully');
    } catch (error) {
      error.message = `Failed to disconnect Kafka client: ${error.message}`;
      throw error;
    }
  }

  async subscribe(topic, messageHandler) {
    try {
      if (!this.isConnected) {
        await this.connect();
      }

      await this.consumer.subscribe({ topic, fromBeginning: false });
      await this.consumer.run({
        eachMessage: async ({ topic, partition, message }) => {
          let messageValue;
          let rawMessage;

          try {
            let value = message.value;
            if (message.headers?.compression === 'snappy' || message.attributes === CompressionTypes.Snappy) {
              value = await handleSnappyDecompression(message.value);
            } else {
              value = message.value.toString();
            }

            rawMessage = value;

            logger.info(`RAW_EMAIL_ALERT_PACKET`, {
              rawAlertPacket: rawMessage,
              eventOffsetId: message.offset,
              partition,
              topic
            });

            messageValue = JSON.parse(value);
            await messageHandler(messageValue);
          } catch (error) {
            logger.error('Failed to process message', {
              error: error.message,
              topic,
              partition,
              offset: message.offset,
              transactionId: messageValue?.transactionId,
              rawAlertPacket: rawMessage,
            });
          }
        },
      });
    } catch (error) {
      error.message = `Failed to subscribe to Kafka topic: ${error.message}`;
      throw error;
    }
  }

  async sendMessage(topic, message) {
    try {
      if (!this.isConnected) {
        await this.connect();
      }
      await this.producer.send({
        topic,
        compression: CompressionTypes.Snappy,
        messages: [{ value: JSON.stringify(message) }],
      });
    } catch (error) {
      error.message = `Failed to send message to Kafka: ${error.message}`;
      throw error;
    }
  }
}

const kafkaConnection = new KafkaConnection();
export default kafkaConnection;
