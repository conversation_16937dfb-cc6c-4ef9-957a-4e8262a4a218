import { Pool } from 'pg';
import moment from 'moment-timezone';
import logger from '../utils/logger.js';

class PostgresConnection {
  constructor() {
    this.pool = null;
  }

  async initialize() {
    try {
      this.pool = new Pool({
        host: process.env.DATABASE_HOST,
        port: process.env.DATABASE_PORT || 5432,
        user: process.env.DATABASE_USER,
        password: process.env.DATABASE_PASSWORD,
        database: process.env.SMART_ALERT_DB_NAME,
        max: 10,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 5000,
      });

      // Test connection
      const client = await this.pool.connect();
      client.release();

      logger.info('PostgreSQL connection initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize PostgreSQL connection', { error: error.message });
      throw error;
    }
  }

  async query(text, params) {
    try {
      const result = await this.pool.query(text, params);
      return result;
    } catch (error) {
      logger.error('PostgreSQL query error', { query: text, error: error.message });
      throw error;
    }
  }

  async unsubscribeUserByEmail(email) {
    const updateResult = await this.query(
      `UPDATE alert_subscribers
       SET status = 0,
           notify_on_email = false,
           unsubscribed_at = $1
       WHERE subscriber_id = $2
       AND status = 1`,
      [moment.tz('UTC').toISOString(), email]
    );

    const unsubscribedCount = updateResult.rowCount;

    if (unsubscribedCount > 0) {
      return {
        unsubscribed: true,
        email,
        subscriberId: email,
        alertCount: unsubscribedCount
      };
    } else {
      return { unsubscribed: false, reason: 'No active subscriptions' };
    }
  }

  async close() {
    if (this.pool) {
      await this.pool.end();
      logger.info('PostgreSQL connection closed');
    }
  }
}

export default new PostgresConnection();
