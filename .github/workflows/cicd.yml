#name: build and deploy jouleone-email-consumer to ecr
#on:
#  push:
#    branches:
#      - main
#  pull_request:
#    branches:
#      - main
#    types:
#    - closed
#env:
#  TAGID: ${{ github.run_number }}
#jobs:
#  build-doc:
#    runs-on: ubuntu-latest
#    steps:
#    - name: Check out branch
#      uses: actions/checkout@v2
#    - name: Configure AWS Credentials
#      uses: aws-actions/configure-aws-credentials@v1
#      with:
#        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
#        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
#        aws-region: us-west-2
#    - name: Login to Amazon ECR
#      uses: aws-actions/amazon-ecr-login@v1
#    - name: Build Process jouleone-email-consumer
#      run: docker build -t 878252606197.dkr.ecr.us-west-2.amazonaws.com/prod-jouleone-email-consumer:$TAGID .
#    - name: Push Production Image to AWS ECR
#      run: docker push 878252606197.dkr.ecr.us-west-2.amazonaws.com/prod-jouleone-email-consumer:$TAGID
