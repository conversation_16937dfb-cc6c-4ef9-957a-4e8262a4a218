import kafkaConnection from '../connection/kafka.js';
import logger from './logger.js';
import kafkaConfig from '../config/kafka.js';

const METRICS_TOPIC = kafkaConfig.topics.metrics;

export async function emitDeliveryMetric(metricPayload) {
  try {
    await kafkaConnection.sendMessage(METRICS_TOPIC, metricPayload);

    logger.debug('Delivery metric emitted successfully', {
      topic: METRICS_TOPIC,
      payload: metricPayload,
    });
  } catch (error) {
    logger.error('Failed to emit delivery metric', {
      topic: METRICS_TOPIC,
      payload: metricPayload,
      error: error.message,
    });
  }
}
