import { createLogger, format, transports } from 'winston';
import * as <PERSON><PERSON> from '@sentry/node';
import SentryTransportModule from 'winston-transport-sentry-node';

const SentryTransport = SentryTransportModule.default || SentryTransportModule;

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV || 'production',
});

const logger = createLogger({
  format: format.json(),
  transports: [
    new transports.Console({
      level: 'info',
    }),
    new SentryTransport({
      sentry: Sentry,
      level: 'error',
    }),
  ],
  exitOnError: false,
});

export default logger;
