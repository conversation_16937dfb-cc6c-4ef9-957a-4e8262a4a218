export const getEmailStyles = (severity, eventName) => {
  return `
    body {
      font-family: Arial, sans-serif;
      line-height: 1.4;
      color: #333;
      background-color: #f5f5f5;
      margin: 0;
      padding: 0;
    }

    p {
      margin: 0 0 6px 0;
    }

    a {
      color: #ff9800;
      text-decoration: none;
    }

    a:hover {
      text-decoration: underline;
    }

    /* Basic reset for certain email clients */
    .ExternalClass {
      width: 100%;
    }

    .ExternalClass,
    .ExternalClass p,
    .ExternalClass span,
    .ExternalClass font,
    .ExternalClass td,
    .ExternalClass div {
      line-height: 100%;
    }

    /* Outlook-specific fixes */
    .ReadMsgBody {
      width: 100%;
    }

    img {
      -ms-interpolation-mode: bicubic;
    }

    /* Mobile styles */
    @media screen and (max-width: 600px) {
      table[class="email-container"] {
        width: 100% !important;
      }

      td[class="mobile-block"] {
        display: block !important;
        width: 100% !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
      }
    }
  `;
};
