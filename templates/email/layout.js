import { getEmailStyles } from './styles.js';

export const generateEmailHtml = ({ metadata, textContent }) => {
  const styles = getEmailStyles(metadata.severity, metadata.eventName);
  const isResolved = metadata.eventName === 'RESOLVED';
  const isEscalated = metadata.eventName === 'ESCALATED';
  const isReminder = metadata.eventName === 'REMINDER';

  const alertDetailUrl = `https://smartjoules.org/${metadata.siteId}/alerts/${metadata.alertInventoryId}`;

  const description = metadata.description || 'DeJoule Intelligence is unable to modulate this asset due to an unknown cause.';

  let headerColor = '#FF0000'; // Default red for OCCURRED

  if (isResolved) {
    headerColor = '#228B22'; // Green for RESOLVED
  } else if (isEscalated) {
    headerColor = '#FF8C00'; // Orange for ESCALATED
  } else if (isReminder) {
    headerColor = '#3366CC'; // Blue for REMINDER
  }

  let severityBackground = '#FFE7E9';
  let severityColor = 'rgb(255, 0, 0)';

  if (metadata.severity && metadata.severity.toLowerCase) {
    const severity = metadata.severity.toLowerCase();
    if (severity === 'medium') {
      severityBackground = 'rgb(248, 223, 175)';
      severityColor = 'rgb(234, 155, 3)';
    } else if (severity === 'high') {
      severityBackground = 'rgb(255, 231, 233)';
      severityColor = 'rgb(130, 47, 43)';
    } else if (severity === 'low') {
      severityBackground = '#D6D6D7';
      severityColor = '#555555';
    }
  }

  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          ${styles}
        </style>
      </head>
      <body>
        <table cellpadding="0" cellspacing="0" border="0" width="100%" bgcolor="#f5f5f5">
          <tr>
            <td align="center" valign="top" style="padding: 15px 0;">
              <!-- Main Email Container -->
              <table cellpadding="0" cellspacing="0" border="0" width="600" class="email-container" bgcolor="#f0f0f0">
                <!-- Header -->
                <tr>
                  <td bgcolor="${headerColor}" style="padding: 10px 16px; border-radius: 4px 4px 0 0; color: white;">
                    <table cellpadding="0" cellspacing="0" border="0" width="100%">
                      <tr>
                        <td>
                          <span style="font-size: 18px; font-weight: normal; vertical-align: middle;">
                            ${isResolved ? 'Alert Resolved' : isEscalated ? 'Alert Escalation' : isReminder ? 'Alert Reminder' : 'New Alert'}
                          </span>
                          <span style="display: inline-block; margin-left: 10px; padding: 3px 10px; border-radius: 3px; background-color: ${severityBackground}; color: ${severityColor}; font-weight: bold; font-size: 11px; text-transform: uppercase; vertical-align: middle;">${metadata.severity ? metadata.severity.toUpperCase() : 'CRITICAL'}</span>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>

                <!-- Greeting -->
                <tr>
                  <td style="padding: 15px 16px 3px 16px;">
                    <p>Dear ${metadata.recipientName || 'User'}</p>
                  </td>
                </tr>

                <!-- Alert Message -->
                <tr>
                  <td style="padding: 0 16px 10px 16px; color: #777;">
                    <p>
                      ${isResolved
                        ? `Alert has been resolved for site ${metadata.siteName || metadata.siteId}`
                        : isEscalated
                          ? `Alert has been escalated for site ${metadata.siteName || metadata.siteId}`
                          : isReminder
                            ? `This is a reminder about an unresolved alert for site ${metadata.siteName || metadata.siteId}`
                            : `You have a new alert for site ${metadata.siteName || metadata.siteId}`
                      }
                    </p>
                  </td>
                </tr>

                <!-- Alert Card -->
                <tr>
                  <td style="padding: 0 16px 15px 16px;">
                    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color: white; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #eaeaea;">
                      <!-- Card Header -->
                      <tr>
                        <td style="padding: 12px 16px 0 16px;">
                          <table cellpadding="0" cellspacing="0" border="0" width="100%" style="margin-bottom: 8px; font-size: 13px;">
                            <tr>
                              <td width="50%" style="text-align: left; padding-right: 10px;">Alert Details</td>
                              <td width="50%" style="text-align: right; text-transform: capitalize; font-weight: bold;">${metadata.alertType} Alert</td>
                            </tr>
                          </table>
                        </td>
                      </tr>

                      <!-- Alert Title -->
                      <tr>
                        <td style="padding: 12px 16px; font-size: 16px; font-weight: bold; color: #333;">
                          ${metadata.customTitle || metadata.assetName}
                        </td>
                      </tr>

                      <!-- Alert Content -->
                      <tr>
                        <td style="padding: 0 16px; color: #777; font-size: 14px;">
                          <p style="margin-bottom: 12px;">${description}</p>
                        </td>
                      </tr>

                      <!-- Alert Footer -->
                      <tr>
                        <td style="padding: 12px 16px;">
                          <!-- Alert ID -->
                          <table cellpadding="0" cellspacing="0" border="0" width="100%" style="margin-bottom: 8px;">
                            <tr>
                              <td>
                                <span style="font-size: 13px; font-weight: bold; display: block;">Alert ID</span>
                                <span style="color: #777; font-size: 14px;">${metadata.alertInventoryId || ''}</span>
                              </td>
                            </tr>
                          </table>

                          <!-- Time Group -->
                          <table cellpadding="0" cellspacing="0" border="0" width="75%" style="margin-bottom: 12px;">
                            <tr>
                              <td width="48%" valign="top">
                                <span style="font-size: 13px; font-weight: bold; display: block;">Occurred</span>
                                <span style="color: #777; font-size: 14px;">${metadata.timestampOccurred || ''}</span>
                              </td>
                              ${isResolved ? `
                              <td width="48%" valign="top">
                                <span style="font-size: 13px; font-weight: bold; display: block;">Resolved at</span>
                                <span style="color: #777; font-size: 14px;">${metadata.timestamp || ''}</span>
                              </td>
                              ` : isReminder ? `
                              <td width="48%" valign="top">
                                <span style="font-size: 13px; font-weight: bold; display: block;">Recent occurrence</span>
                                <span style="color: #777; font-size: 14px;">${metadata.timestamp || ''}</span>
                              </td>
                              ` : ''}
                            </tr>
                          </table>

                          <!-- Know More -->
                          <table cellpadding="0" cellspacing="0" border="0" width="100%">
                            <tr>
                              <td align="right">
                                <a href="${alertDetailUrl}" style="color: #ff9800; text-decoration: none; font-weight: bold; font-size: 14px;">Know more &rarr;</a>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>

                <!-- Signature -->
                <tr>
                  <td style="padding: 0 16px 15px 16px; color: #777; font-size: 14px;">
                    <p>
                      ${isResolved
                        ? 'Thank you for your contribution towards Global Sustainability Goals.'
                        : 'Thank you for your prompt attention.'
                      }
                    </p>
                    <p style="margin-top: 10px;">
                      Best Regards,<br>
                      Smart Joules Team
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>
  `;
};
