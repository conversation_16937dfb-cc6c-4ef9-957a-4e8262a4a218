import moment from 'moment-timezone';

const formatTimestamp = (timestamp) => {
  try {
    return moment(timestamp).tz('Asia/Kolkata').format('DD MMM YYYY, hh:mm:ss A');
  } catch (error) {
    return timestamp;
  }
};

const getCommonTitle = (metadata) =>
  `Alert: ${metadata.content?.title}`;

const emailTemplates = {
  RESOLVED: {
    getTitle: getCommonTitle,
    getBody: (metadata, title, recipient) => {
      metadata.recipientName = recipient.name;
      metadata.customTitle = title;

      if (metadata.timestampOccurred) {
        metadata.timestampOccurred = formatTimestamp(metadata.timestampOccurred);
      }
      if (metadata.timestamp) {
        metadata.timestamp = formatTimestamp(metadata.timestamp);
      }

      return '';
    },
  },

  REMINDER: {
    getTitle: getCommonTitle,
    getBody: (metadata, title, recipient) => {
      metadata.recipientName = recipient.name;
      metadata.customTitle = title;

      if (metadata.timestampOccurred) {
        metadata.timestampOccurred = formatTimestamp(metadata.timestampOccurred);
      }
      if (metadata.timestamp) {
        metadata.timestamp = formatTimestamp(metadata.timestamp);
      }

      return '';
    },
  },

  ESCALATED: {
    getTitle: getCommonTitle,
    getBody: (metadata, title, recipient) => {
      metadata.recipientName = recipient.name;
      metadata.customTitle = title;

      if (metadata.timestamp) {
        metadata.timestampOccurred = formatTimestamp(metadata.timestamp);
      }

      return '';
    },
  },

  OCCURRED: {
    getTitle: getCommonTitle,
    getBody: (metadata, title, recipient) => {
      metadata.recipientName = recipient.name;
      metadata.customTitle = title;

      if (metadata.timestamp) {
        metadata.timestampOccurred = formatTimestamp(metadata.timestamp);
      }

      return '';
    },
  },
};

export const getEmailTemplate = (eventName) => {
  const template = emailTemplates[eventName.toUpperCase()];
  if (!template) {
    throw new Error(`No template found for event: ${eventName}`);
  }
  return template;
};
