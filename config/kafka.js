export default {
  security: {
    protocol: process.env.KAFKA_SECURITY_PROTOCOL || 'PLAIN',
    username: process.env.<PERSON><PERSON>KA_USERNAME,
    password: process.env.<PERSON>AFKA_PASSWORD,
  },
  brokers: (process.env.KAFKA_BROKERS || 'localhost:9092').split(','),
  connectionTimeout: parseInt(process.env.KAFKA_CONNECTION_TIMEOUT || '30000', 10),
  topics: {
    emailNotification: process.env.KAFKA_TOPIC_EMAIL_NOTIFICATION || 'SmartAlert.Notification.Email',
    metrics: process.env.KAFKA_TOPIC_METRICS || 'SmartAlert.Metrics',
  },
};
